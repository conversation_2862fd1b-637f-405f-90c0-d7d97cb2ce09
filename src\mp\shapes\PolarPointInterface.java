package mp.shapes;

public interface PolarPointInterface extends Locatable{
	public double getAngle();
	public double getRadius();
}

// Abstract base class for PolarPointInterface implementations
abstract class AbstractPolarPoint implements PolarPointInterface {
    protected double radius, angle;

    public AbstractPolarPoint(double radius, double angle) {
        this.radius = radius;
        this.angle = angle;
    }

    public AbstractPolarPoint(int x, int y) {
        this.radius = Math.sqrt(x*x + y*y);
        this.angle = Math.atan((double) y/x);
    }

    @Override
    public int getX() {
        return (int) (radius*Math.cos(angle));
    }

    @Override
    public int getY() {
        return (int) (radius*Math.sin(angle));
    }

    @Override
    public double getAngle() {
        return angle;
    }

    @Override
    public double getRadius() {
        return radius;
    }

    @Override
    public void setX(int x) {
        // Empty implementation for polar coordinates
    }

    @Override
    public void setY(int y) {
        // Empty implementation for polar coordinates
    }
}
