package main;
import mp.bridge.Angle;
import mp.bridge.Shape;
import mp.shapes.ConsoleScene;
import mp.shapes.ConsoleSceneInterface;
import mp.bridge.BridgeScene;
import tags301.Comp301Tags;
import util.annotations.Tags;
@Tags(Comp301Tags.FACTORY_CLASS)
public class StaticFactoryClass{
static BridgeScene scene;	
private static ConsoleSceneInterface consoleView;
@Tags(Comp301Tags.BRIDGE_SCENE)
public static BridgeScene bridgeSceneFactoryMethod(){
   if (scene == null){scene = new BridgeSceneImpl();}
   return scene;
}
@Tags(Comp301Tags.CONSOLE_SCENE_VIEW)
public static ConsoleSceneInterface consoleSceneViewFactoryMethod() {
  if (consoleView == null) {
    consoleView = ConsoleScene.consoleSceneViewFactoryMethod();
  }
  return consoleView;
}
@Tags(Comp301Tags.ANGLE)
public static Angle legsFactoryMethod(){
	   return new Shape();		
}
}