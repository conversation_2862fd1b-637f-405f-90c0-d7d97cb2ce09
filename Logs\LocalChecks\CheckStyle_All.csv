-1,<PERSON><PERSON> Jul 08 22:29:56 EDT 2025,true,mp\bridge\RobinHead.java,nameNotInDictionary,fn,fn
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\RobinHead.java,minimumVowelInNameCheck,fn,fn,1
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\RobinHead.java,nameInDictionary,mp.bridge.RobinHead,bridge
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\RobinHead.java,nameInDictionary,mp.bridge.RobinHead,robin
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\RobinHead.java,nameInDictionary,mp.bridge.Robin<PERSON><PERSON>,head
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\RobinHead.java,nameInDictionary,x,x
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\RobinHead.java,nameInDictionary,y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\RobinHead.java,missingGetter,StringShape,@STRING_PATTERN,mp.bridge.RobinHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\RobinHead.java,missingGetter,Head,@IMAGE_PATTERN,mp.bridge.RobinHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\RobinHead.java,missingGetter,Arms,@Comp301Tags.ANGLE,mp.bridge.RobinHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\RobinHead.java,missingGetter,Legs,@Comp301Tags.ANGLE,mp.bridge.RobinHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\RobinHead.java,missingSignature,move:int;int->void,mp.bridge.RobinHead:[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\RobinHead.java,missingSignature,scale:double->void//EC,mp.bridge.RobinHead:[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\RobinHead.java,publicMethodsOverride,[]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ImageShape.java,nameNotInDictionary,fn,fn
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ImageShape.java,nameInDictionary,mp.bridge.ImageShape,bridge
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ImageShape.java,nameInDictionary,mp.bridge.ImageShape,image
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ImageShape.java,nameInDictionary,mp.bridge.ImageShape,shape
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ImageShape.java,nameInDictionary,x,x
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ImageShape.java,nameInDictionary,y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameNotInDictionary,c1,c
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,minimumLettersInNameCheck,c1,c,2
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,minimumVowelInNameCheck,c1,c,1
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameNotInDictionary,c2,c
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,minimumLettersInNameCheck,c2,c,2
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,minimumVowelInNameCheck,c2,c,1
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,mp.shapes.Gorge,shapes
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,mp.shapes.Gorge,gorge
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,RIGHT_LINE_X,right
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,RIGHT_LINE_X,line
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,RIGHT_LINE_X,x
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,LINE_TOP_Y,line
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,LINE_TOP_Y,top
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,LINE_TOP_Y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,LINE_HEIGHT,line
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,LINE_HEIGHT,height
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,upper,upper
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,lower,lower
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,leftLine,left
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,leftLine,line
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,rightLine,right
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,rightLine,line
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,nameInDictionary,rectangle,rectangle
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,missingGetter,Width,int,mp.shapes.Gorge[@Comp301Tags.BOUNDED_SHAPE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,missingGetter,Height,int,mp.shapes.Gorge[@Comp301Tags.BOUNDED_SHAPE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,missingSuperType,@Comp301Tags.LOCATABLE,mp.shapes.Gorge[@Comp301Tags.BOUNDED_SHAPE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,missingInstantiation,java.beans.PropertyChangeEvent,mp.shapes.Gorge[@Comp301Tags.BOUNDED_SHAPE],no method
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,publicMethodsDoNotOverride,[public  getLeftLine:->mp.shapes.RotatingLine, public  getRightLine:->mp.shapes.RotatingLine, public  getRectangle:->mp.shapes.AScalableRectangle]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,missingSetter,Width,int,mp.shapes.Gorge[@Comp301Tags.BOUNDED_SHAPE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Gorge.java,missingSetter,Height,int,mp.shapes.Gorge[@Comp301Tags.BOUNDED_SHAPE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Angle.java,nameNotInDictionary,dx,dx
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Angle.java,minimumVowelInNameCheck,dx,dx,1
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Angle.java,nameNotInDictionary,dy,dy
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Angle.java,minimumVowelInNameCheck,dy,dy,1
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Angle.java,nameInDictionary,mp.bridge.Angle,bridge
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Angle.java,nameInDictionary,mp.bridge.Angle,angle
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotateLine.java,nameNotInDictionary,r,r
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotateLine.java,minimumLettersInNameCheck,r,r,2
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotateLine.java,minimumVowelInNameCheck,r,r,1
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotateLine.java,nameInDictionary,mp.shapes.RotateLine,shapes
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotateLine.java,nameInDictionary,mp.shapes.RotateLine,rotate
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotateLine.java,nameInDictionary,mp.shapes.RotateLine,line
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotateLine.java,nameInDictionary,x,x
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotateLine.java,nameInDictionary,y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotateLine.java,nameInDictionary,angle,angle
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotateLine.java,nameInDictionary,units,units
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotateLine.java,variableHasInterfaceType,RotateLine,left
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotateLine.java,variableHasInterfaceType,RotateLine,right
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotateLine.java,variableHasInterfaceType,RotateLine,getLeftLine
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotateLine.java,variableHasInterfaceType,RotateLine,getRightLine
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,nameNotInDictionary,fn,fn
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,nameInDictionary,mp.bridge.GuardHead,bridge
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,nameInDictionary,mp.bridge.GuardHead,guard
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,nameInDictionary,mp.bridge.GuardHead,head
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,nameInDictionary,x,x
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,nameInDictionary,y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,missingGetter,StringShape,@STRING_PATTERN,mp.bridge.GuardHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,missingGetter,Head,@IMAGE_PATTERN,mp.bridge.GuardHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,missingGetter,Arms,@Comp301Tags.ANGLE,mp.bridge.GuardHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,missingGetter,Legs,@Comp301Tags.ANGLE,mp.bridge.GuardHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getImageFileName:->String
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setImageFileName:String->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setX:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setY:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,missingSignature,move:int;int->void,mp.bridge.GuardHead:[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,missingSignature,scale:double->void//EC,mp.bridge.GuardHead:[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GuardHead.java,publicMethodsOverride,[]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,nameNotInDictionary,fn,fn
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,nameInDictionary,mp.bridge.GalahadHead,bridge
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,nameInDictionary,mp.bridge.GalahadHead,galahad
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,nameInDictionary,mp.bridge.GalahadHead,head
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,nameInDictionary,x,x
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,nameInDictionary,y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,missingGetter,StringShape,@STRING_PATTERN,mp.bridge.GalahadHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,missingGetter,Head,@IMAGE_PATTERN,mp.bridge.GalahadHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,missingGetter,Arms,@Comp301Tags.ANGLE,mp.bridge.GalahadHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,missingGetter,Legs,@Comp301Tags.ANGLE,mp.bridge.GalahadHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getImageFileName:->String
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setImageFileName:String->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setX:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setY:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),GalahadHead,mp.bridge.GuardHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),GalahadHead,mp.bridge.GuardHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),GalahadHead,mp.bridge.GuardHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,missingSignature,move:int;int->void,mp.bridge.GalahadHead:[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,missingSignature,scale:double->void//EC,mp.bridge.GalahadHead:[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\GalahadHead.java,publicMethodsOverride,[]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,nameNotInDictionary,fn,fn
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,nameInDictionary,mp.bridge.LancelotHead,bridge
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,nameInDictionary,mp.bridge.LancelotHead,lancelot
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,nameInDictionary,mp.bridge.LancelotHead,head
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,nameInDictionary,x,x
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,nameInDictionary,y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,missingGetter,StringShape,@STRING_PATTERN,mp.bridge.LancelotHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,missingGetter,Head,@IMAGE_PATTERN,mp.bridge.LancelotHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,missingGetter,Arms,@Comp301Tags.ANGLE,mp.bridge.LancelotHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,missingGetter,Legs,@Comp301Tags.ANGLE,mp.bridge.LancelotHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getImageFileName:->String
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setImageFileName:String->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setX:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setY:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),LancelotHead,mp.bridge.GuardHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),LancelotHead,mp.bridge.GuardHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),LancelotHead,mp.bridge.GuardHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),LancelotHead,mp.bridge.GalahadHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),LancelotHead,mp.bridge.GalahadHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),LancelotHead,mp.bridge.GalahadHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.bridge.GuardHead,public  getImageFileName:->String,[mp.bridge.ImageShape]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.bridge.GuardHead,public  setImageFileName:String->void,[mp.bridge.ImageShape]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.bridge.GuardHead,public  getX:->int,[mp.bridge.ImageShape]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.bridge.GuardHead,public  setX:int->void,[mp.bridge.ImageShape]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.bridge.GuardHead,public  getY:->int,[mp.bridge.ImageShape]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.bridge.GuardHead,public  setY:int->void,[mp.bridge.ImageShape]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.bridge.GalahadHead,public  getImageFileName:->String,[mp.bridge.ImageShape]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.bridge.GalahadHead,public  setImageFileName:String->void,[mp.bridge.ImageShape]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.bridge.GalahadHead,public  getX:->int,[mp.bridge.ImageShape]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.bridge.GalahadHead,public  setX:int->void,[mp.bridge.ImageShape]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.bridge.GalahadHead,public  getY:->int,[mp.bridge.ImageShape]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,peerOverriddingSignatures,mp.bridge.GalahadHead,public  setY:int->void,[mp.bridge.ImageShape]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,missingSignature,move:int;int->void,mp.bridge.LancelotHead:[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,missingSignature,scale:double->void//EC,mp.bridge.LancelotHead:[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\LancelotHead.java,publicMethodsOverride,[]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,nameNotInDictionary,b,b
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,minimumLettersInNameCheck,b,b,2
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,minimumVowelInNameCheck,b,b,1
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,nameNotInDictionary,r,r
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,nameNotInDictionary,dx,dx
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,nameNotInDictionary,dy,dy
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,nameInDictionary,mp.shapes.RotatingLine,shapes
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,nameInDictionary,mp.shapes.RotatingLine,rotating
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,nameInDictionary,mp.shapes.RotatingLine,line
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,nameInDictionary,point,point
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,nameInDictionary,a,a
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,nameInDictionary,UNIT,unit
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,nameInDictionary,x,x
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,nameInDictionary,y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,nameInDictionary,angle,angle
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,nameInDictionary,units,units
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  setX:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  setY:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setX:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setY:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),RotatingLine,mp.bridge.GuardHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),RotatingLine,mp.bridge.GuardHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),RotatingLine,mp.bridge.GalahadHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),RotatingLine,mp.bridge.GalahadHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),RotatingLine,mp.bridge.LancelotHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),RotatingLine,mp.bridge.LancelotHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,expectedGetter,X,int,mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,expectedGetter,Y,int,mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,expectedGetter,Radius,double,mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,expectedGetter,Angle,double,mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,expectedSignature,rotate:int->void,mp.shapes.RotatingLine:[@Comp301Tags.ROTATING_LINE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,expectedSignature,move:int;int->void,mp.shapes.RotatingLine:[@Comp301Tags.ROTATING_LINE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,variableHasClassType,RotatingLine,leftLine
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,variableHasClassType,RotatingLine,rightLine
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,variableHasClassType,RotatingLine,getLeftLine
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,variableHasClassType,RotatingLine,getRightLine
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,publicMethodsOverride,[]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,expectedSetter,X,int,mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,expectedSetter,Y,int,mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,expectedSetter,Radius,double,mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\RotatingLine.java,expectedSetter,Angle,double,mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,nameInDictionary,mp.shapes.APolarPoint,shapes
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,nameInDictionary,mp.shapes.APolarPoint,a
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,nameInDictionary,mp.shapes.APolarPoint,polar
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,nameInDictionary,mp.shapes.APolarPoint,point
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,nameInDictionary,radius,radius
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,nameInDictionary,angle,angle
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.bridge.GuardHead,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getAngle:->double
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.shapes.RotatingLine,public  getRadius:->double
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.bridge.LancelotHead,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,peerDuplicatedSignatures,mp.bridge.LancelotHead,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\APolarPoint.java,publicMethodsOverride,[]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\BridgeScene.java,nameInDictionary,mp.bridge.BridgeScene,bridge
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\BridgeScene.java,nameInDictionary,mp.bridge.BridgeScene,scene
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Avatar.java,nameNotInDictionary,dx,dx
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Avatar.java,nameNotInDictionary,dy,dy
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Avatar.java,nameInDictionary,mp.bridge.Avatar,bridge
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Avatar.java,nameInDictionary,mp.bridge.Avatar,avatar
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,ImageShape,getHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,Angle,getArms
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,Angle,getLegs
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,getArthur
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,getLancelot
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,getRobin
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,getGalahad
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\Avatar.java,variableHasInterfaceType,Avatar,getGuard
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,nameNotInDictionary,t,t
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,minimumLettersInNameCheck,t,t,2
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,minimumVowelInNameCheck,t,t,1
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,nameInDictionary,mp.bridge.SpeechBubble,bridge
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,nameInDictionary,mp.bridge.SpeechBubble,speech
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,nameInDictionary,mp.bridge.SpeechBubble,bubble
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,nameInDictionary,text,text
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,nameInDictionary,x,x
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,nameInDictionary,y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  setX:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  setY:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setX:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setY:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,missingSuperType,@Comp301Tags.LOCATABLE,mp.bridge.SpeechBubble
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\SpeechBubble.java,publicMethodsOverride,[]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,nameNotInDictionary,fn,fn
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,nameInDictionary,mp.bridge.ArthurHead,bridge
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,nameInDictionary,mp.bridge.ArthurHead,arthur
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,nameInDictionary,mp.bridge.ArthurHead,head
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,nameInDictionary,x,x
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,nameInDictionary,y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,missingGetter,StringShape,@STRING_PATTERN,mp.bridge.ArthurHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,missingGetter,Head,@IMAGE_PATTERN,mp.bridge.ArthurHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,missingGetter,Arms,@Comp301Tags.ANGLE,mp.bridge.ArthurHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,missingGetter,Legs,@Comp301Tags.ANGLE,mp.bridge.ArthurHead[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getImageFileName:->String
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setImageFileName:String->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setX:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  setY:int->void
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),ArthurHead,mp.bridge.GuardHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),ArthurHead,mp.bridge.GuardHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),ArthurHead,mp.bridge.GuardHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),ArthurHead,mp.bridge.GalahadHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),ArthurHead,mp.bridge.GalahadHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),ArthurHead,mp.bridge.GalahadHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),ArthurHead,mp.shapes.RotatingLine
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),ArthurHead,mp.shapes.RotatingLine
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ),ArthurHead,mp.bridge.LancelotHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public X:int(public ,public ),ArthurHead,mp.bridge.LancelotHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:3 access:public Y:int(public ,public ),ArthurHead,mp.bridge.LancelotHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,missingSignature,move:int;int->void,mp.bridge.ArthurHead:[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,missingSignature,scale:double->void//EC,mp.bridge.ArthurHead:[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\ArthurHead.java,publicMethodsOverride,[]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameNotInDictionary,mp.shapes.AScalableRectangle,scalable
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameInDictionary,mp.shapes.AScalableRectangle,shapes
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameInDictionary,mp.shapes.AScalableRectangle,a
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameInDictionary,mp.shapes.AScalableRectangle,rectangle
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameInDictionary,x,x
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameInDictionary,y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameInDictionary,width,width
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameInDictionary,height,height
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameInDictionary,newVal,new
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameInDictionary,newVal,val
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,nameInDictionary,percentage,percentage
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,liberalMagicNumber,100,100
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDuplicatedSignatures,mp.bridge.GalahadHead,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDuplicatedSignatures,mp.bridge.RobinHead,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDuplicatedSignatures,mp.bridge.ArthurHead,public  getX:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDuplicatedSignatures,mp.bridge.ArthurHead,public  getY:->int
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public X:int(public , null),AScalableRectangle,mp.bridge.GuardHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public Y:int(public , null),AScalableRectangle,mp.bridge.GuardHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public X:int(public , null),AScalableRectangle,mp.bridge.GalahadHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public Y:int(public , null),AScalableRectangle,mp.bridge.GalahadHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public X:int(public , null),AScalableRectangle,mp.shapes.RotatingLine
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public Y:int(public , null),AScalableRectangle,mp.shapes.RotatingLine
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:2 access:public Height:int(public ,public ),AScalableRectangle,mp.shapes.RotatingLine
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,editable, g-s:0 p-v:2 access:public Width:int(public ,public ),AScalableRectangle,mp.shapes.RotatingLine
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public X:int(public , null),AScalableRectangle,mp.bridge.ArthurHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public Y:int(public , null),AScalableRectangle,mp.bridge.ArthurHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public X:int(public , null),AScalableRectangle,mp.bridge.LancelotHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,peerDoesNotHaveCommonProperties,readonly  p-v:2 access:public Y:int(public , null),AScalableRectangle,mp.bridge.LancelotHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\AScalableRectangle.java,publicMethodsDoNotOverride,[public  getX:->int, public  getY:->int, public  getWidth:->int, public  getHeight:->int, public  setHeight:int->void, public  setWidth:int->void, public  scale:int->void]
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameNotInDictionary,lance_const,const
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameNotInDictionary,robin_const,const
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameNotInDictionary,gal_const,const
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameNotInDictionary,guard_const,const
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameNotInDictionary,s,s
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,minimumLettersInNameCheck,s,s,2
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,minimumVowelInNameCheck,s,s,1
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,main.BridgeSceneImpl,main
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,main.BridgeSceneImpl,bridge
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,main.BridgeSceneImpl,scene
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,main.BridgeSceneImpl,impl
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,arthur,arthur
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,lancelot,lancelot
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,robin,robin
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,galahad,galahad
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,guard,guard
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,some_x,some
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,some_x,x
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,some_y,some
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,some_y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,lance_const,lance
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,robin_const,robin
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,gal_const,gal
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,guard_const,guard
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,gorge,gorge
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,cur,cur
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,knightArea,knight
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,knightArea,area
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,guardArea,guard
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,guardArea,area
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,KnightTurn,knight
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,KnightTurn,turn
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,AREA_X,area
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,AREA_X,x
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,KNIGHT_Y,knight
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,KNIGHT_Y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,GUARD_Y,guard
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,GUARD_Y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,AREA_WIDTH,area
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,AREA_WIDTH,width
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,AREA_HEIGHT,area
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,AREA_HEIGHT,height
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,Occupied,occupied
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nameInDictionary,avatar,avatar
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,arthur
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,lancelot
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,robin
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,galahad
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,guard
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,liberalMagicNumber,750,EXPR 750
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,bulkierThen,60.0,5.0,12.0
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,thenBranching,say
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,nestedIfDepth,2,1
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,getArthur
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,getLancelot
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,getRobin
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,getGalahad
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,variableHasInterfaceType,Avatar,getGuard
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,publicMethodsDoNotOverride,[public  approach:mp.bridge.AvatarImpl->void, public  say:String->void, public  getKnightArea:->mp.shapes.AScalableRectangle, public  getGuardArea:->mp.shapes.AScalableRectangle, public  getGorge:->mp.shapes.Gorge]
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,expectedInstantiation,@Comp301Tags.AVATAR,main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE],[public  BridgeSceneImpl:->]
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,expectedGetter,Arthur,@Comp301Tags.AVATAR,main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,expectedGetter,Galahad,@Comp301Tags.AVATAR,main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,expectedGetter,Lancelot,@Comp301Tags.AVATAR,main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,expectedGetter,Robin,@Comp301Tags.AVATAR,main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\BridgeSceneImpl.java,expectedGetter,Guard,@Comp301Tags.AVATAR,main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,nameNotInDictionary,dx,dx
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,nameNotInDictionary,dy,dy
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,nameInDictionary,mp.bridge.AvatarImpl,bridge
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,nameInDictionary,mp.bridge.AvatarImpl,avatar
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,nameInDictionary,mp.bridge.AvatarImpl,impl
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,nameInDictionary,head,head
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,nameInDictionary,speech,speech
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,nameInDictionary,arms,arms
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,nameInDictionary,legs,legs
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,variableHasInterfaceType,ImageShape,head
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,variableHasInterfaceType,Angle,arms
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,variableHasInterfaceType,Angle,legs
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,variableHasInterfaceType,ImageShape,getHead
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,variableHasInterfaceType,Angle,getArms
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,variableHasInterfaceType,Angle,getLegs
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,missingSignature,approach:@Comp301Tags.AVATAR->void,mp.bridge.AvatarImpl:[@Comp301Tags.BRIDGE_SCENE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,missingSignature,say:String->void,mp.bridge.AvatarImpl:[@Comp301Tags.BRIDGE_SCENE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,missingSignature,passed:->void,mp.bridge.AvatarImpl:[@Comp301Tags.BRIDGE_SCENE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,missingSignature,failed:->void,mp.bridge.AvatarImpl:[@Comp301Tags.BRIDGE_SCENE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,missingSignature,@scroll:int;int->void//EC,mp.bridge.AvatarImpl:[@Comp301Tags.BRIDGE_SCENE]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,expectedSignature,move:int;int->void,mp.bridge.AvatarImpl:[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,expectedInstantiation,@Comp301Tags.ANGLE,mp.bridge.AvatarImpl[@Comp301Tags.AVATAR],[public  AvatarImpl:mp.bridge.ImageShape->]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,missingSignature,scale:double->void//EC,mp.bridge.AvatarImpl:[@Comp301Tags.AVATAR]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,variableHasClassType,AvatarImpl,cur
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,variableHasClassType,AvatarImpl,avatar
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\AvatarImpl.java,publicMethodsOverride,[]
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Point.java,nameInDictionary,mp.shapes.Point,shapes
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Point.java,nameInDictionary,mp.shapes.Point,point
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\shapes\Point.java,variableHasInterfaceType,Point,point
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\StringShape.java,nameNotInDictionary,t,t
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\StringShape.java,nameInDictionary,mp.bridge.StringShape,bridge
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\StringShape.java,nameInDictionary,mp.bridge.StringShape,string
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\StringShape.java,nameInDictionary,mp.bridge.StringShape,shape
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\StringShape.java,nameInDictionary,x,x
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\StringShape.java,nameInDictionary,y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\StringShape.java,variableHasInterfaceType,StringShape,getStringShape
-1,Tue Jul 08 22:29:56 EDT 2025,true,mp\bridge\StringShape.java,variableHasInterfaceType,StringShape,speech
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\RunSS25A2Tests.java,nameInDictionary,main.RunSS25A2Tests,main
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\RunSS25A2Tests.java,nameInDictionary,main.RunSS25A2Tests,run
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\RunSS25A2Tests.java,nameInDictionary,main.RunSS25A2Tests,a
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\RunSS25A2Tests.java,nameInDictionary,main.RunSS25A2Tests,tests
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\RunSS25A2Tests.java,nameInDictionary,args,args
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\RunSS25A2Tests.java,liberalMagicNumber,600,EXPR 600
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\RunSS25A2Tests.java,liberalMagicNumber,2000,EXPR 2000
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\RunSS25A2Tests.java,liberalMagicNumber,5,EXPR 5
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameNotInDictionary,SOME_RAD,rad
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameNotInDictionary,D,d
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,minimumLettersInNameCheck,D,d,2
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,minimumVowelInNameCheck,D,d,1
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,main.Assignment1,main
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,main.Assignment1,assignment
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,SOME_RAD,some
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,SOME_ANGLE,some
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,SOME_ANGLE,angle
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,START_X,start
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,START_X,x
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,START_Y,start
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,START_Y,y
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,COUNT,count
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,SLEEP_MS,sleep
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,SLEEP_MS,ms
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,line,line
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,frame,frame
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,i,i
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,nameInDictionary,args,args
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,variableHasInterfaceType,RotateLine,line
-1,Tue Jul 08 22:29:56 EDT 2025,true,main\Assignment1.java,expectedInstantiation,@Comp301Tags.BRIDGE_SCENE,main.Assignment1[main.Assignment1],[static public  main:String[]->void]
