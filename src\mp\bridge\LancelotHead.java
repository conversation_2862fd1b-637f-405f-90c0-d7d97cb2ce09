package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class LancelotHead extends AbstractImageShape{
    public LancelotHead() {
        super("images/lancelot.jpg");
    }
}
