package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScaleRectangleInterface;
import mp.shapes.GetRectangle;
import tags301.Comp301Tags;

@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface BridgeScene {
    Avatar getArthur();
    Avatar getLancelot();
    Avatar getRobin();
    Avatar getGalahad();
    Avatar getGuard();
    public void passed();
    public void failed();
    public void approach(final Avatar avatar);
    public void say(final String say);
    public AScaleRectangleInterface getKnightArea();
    public AScaleRectangleInterface getGuardArea();
    public GetRectangle getGorge();
    public boolean getOccupied();
    public boolean getKnightTurn();
    public Avatar getInteractingKnight();
}