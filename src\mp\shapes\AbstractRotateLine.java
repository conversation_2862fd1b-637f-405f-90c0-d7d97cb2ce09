package mp.shapes;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Visible;
import util.models.PropertyListenerRegisterer;
import tags301.Comp301Tags;

@Tags({Comp301Tags.LOCATABLE})
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public abstract class AbstractRotateLine implements RotateLine {
    
    // Abstract method that subclasses must implement for property change notification
    @Visible(false)
    public abstract void notify(String property, Object old, Object current);
}
