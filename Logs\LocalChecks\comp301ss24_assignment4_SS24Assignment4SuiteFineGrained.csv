#,Time,%Passes,Change,Test,Pass,Partial,Fail,Untested,SessionNumber,SessionRunNumber,IsSuite,SuiteTests,PrerequisiteTests,ExtraCreditTests,TestScores,FailFromPreReq,
0,Wed Jul 09 21:17:47 EDT 2025,0,0,A4Style, , ,A4CommonPropertiesAreInherited+ A4CommonSignaturesAreInherited+ A4Encapsulation+ A4InterfaceAsType+ A4MnemonicNames+ A4NamedConstants+ A4NamingConventions+ A4NoHiddenFields+ A4NoStarImports+ A4NonPublicAccessModifiersMatched+ A4PackageDeclarations+ A4PublicMethodsOverride+ A4SimplifyBooleanExpressions+ A4SimplifyBooleanReturns+ AssertingBridgeSceneDynamics+ ,AbstractBoundedShape AbstractLocatable AbstractionAsyncArthurAnimation AbstractionLockstepAvatars AbstractionSyncArthurAnimation AbstractionWaitingAvatars ArthurIsNotAContortionist AsyncArthurAnimation AsyncGalahadAnimation AsyncLancelotAnimation AsyncRobinAnimation BridgeSceneSingletonFromFactory BroadcastingClearanceManagerFactoryMethodDefined BroadcastingClearanceManagerSingletonFromFactory CheckedImpossibleAngle LockstepAvatars SceneControllerApproachButtonProperty SceneControllerButtonDynamics SceneControllerCallsApproach SceneControllerCallsFailed SceneControllerCallsPassed SceneControllerCallsSay SceneControllerCallsSetEnabled SceneControllerISAPropertyChangeListener SceneControllerISAnActionListener SceneControllerPassedButtonProperty SceneControllerRegistersAsActionListener SceneControllerRegistersAsPropertyChangeListener SceneControllerSayButtonProperty SceneControllerSingletonFromFactory SyncArthurAnimation SyncGalahadAnimation SyncLancelotAnimation SyncRobinAnimation TaggedBoundedShape TaggedImpossibleAngle TaggedLegs TaggedLocatable TaggedRestrictedLine WaitingAvatars ,0,0,true,A4CommonPropertiesAreInherited A4CommonSignaturesAreInherited A4Encapsulation A4InterfaceAsType A4MnemonicNames A4NamedConstants A4NamingConventions A4NoHiddenFields A4NoStarImports A4NonPublicAccessModifiersMatched A4PackageDeclarations A4PublicMethodsOverride A4SimplifyBooleanExpressions A4SimplifyBooleanReturns ,AssertingBridgeSceneDynamics ,A4CommonPropertiesAreInherited A4CommonSignaturesAreInherited A4NonPublicAccessModifiersMatched ,A4CommonPropertiesAreInherited-(0.0/2.0) A4CommonSignaturesAreInherited-(0.0/2.0) A4Encapsulation-(0.0/3.0) A4InterfaceAsType-(0.0/7.0) A4MnemonicNames-(0.0/5.0) A4NamedConstants-(0.0/3.0) A4NamingConventions-(0.0/2.0) A4NoHiddenFields-(0.0/1.0) A4NoStarImports-(0.0/1.0) A4NonPublicAccessModifiersMatched-(0.0/5.0) A4PackageDeclarations-(0.0/2.0) A4PublicMethodsOverride-(0.0/5.0) A4SimplifyBooleanExpressions-(0.0/1.0) A4SimplifyBooleanReturns-(0.0/1.0) , ,
