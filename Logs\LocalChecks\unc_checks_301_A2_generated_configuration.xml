	<module name="ClassDefined">
		<property name="severity" value="info"/>
		<property name="expectedTypes" value="
			@Comp301Tags.ANGLE,
			@Comp301Tags.AVATAR,
			@Comp301Tags.BOUNDED_SHAPE,
			@<EMAIL>,
			@Comp301Tags.BRIDGE_SCENE,
			@Comp301Tags.CONSOLE_SCENE_VIEW,
			@Comp301Tags.FACTORY_CLASS,
			@Comp301Tags.LOCATABLE,
			@main.Assignment2,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@main.Assignment2"/>
		<property name="expectedSignatures" value="
			animateLine:->.*,
			main:String[]->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@main.Assignment2"/>
		<property name="expectedSignatures" value="
			animateLine:->.*,
			main:String[]->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@main.Assignment2"/>
		<property name="expectedCalls" value="
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			@Comp301Tags.FACTORY_CLASS!legsFactoryMethod:->@Comp301Tags.ANGLE,
			bus.uigen.ObjectEditor!edit:Object->.*,
			mp.shapes.RotateLine!setAngle:double->void,
			mp.shapes.Locatable!setX:*->.*,
			@Comp301Tags.AVATAR!move:*->.*,
			mp.shapes.RotateLine!setRadius:double->void,
			@Comp301Tags.FACTORY_CLASS!bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getHeight:*->.*,
			@Comp301Tags.LOCATABLE!getWidth:->int,
			@Comp301Tags.LOCATABLE!setX:*->.*,
			mp.shapes.Locatable!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			bus.uigen.OEFrame!refresh:->void,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.ANGLE!move:*->.*,
			java.lang.Math!sin:double->double,
			@Comp301Tags.LOCATABLE!move:*->.*,
			mp.shapes.Locatable!setY:*->.*,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			java.lang.Math!cos:double->double,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			mp.shapes.Locatable!getX:*->.*,
			java.lang.Thread!sleep:long->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@main.Assignment2"/>
		<property name="expectedCalls" value="
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			@Comp301Tags.FACTORY_CLASS!legsFactoryMethod:->@Comp301Tags.ANGLE,
			bus.uigen.ObjectEditor!edit:Object->.*,
			mp.shapes.RotateLine!setAngle:double->void,
			mp.shapes.Locatable!setX:*->.*,
			@Comp301Tags.AVATAR!move:*->.*,
			mp.shapes.RotateLine!setRadius:double->void,
			@Comp301Tags.FACTORY_CLASS!bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getHeight:*->.*,
			@Comp301Tags.LOCATABLE!getWidth:->int,
			@Comp301Tags.LOCATABLE!setX:*->.*,
			mp.shapes.Locatable!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			bus.uigen.OEFrame!refresh:->void,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.ANGLE!move:*->.*,
			java.lang.Math!sin:double->double,
			@Comp301Tags.LOCATABLE!move:*->.*,
			mp.shapes.Locatable!setY:*->.*,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			java.lang.Math!cos:double->double,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			mp.shapes.Locatable!getX:*->.*,
			java.lang.Thread!sleep:long->void,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedProperties" value="
			KnightTurn:boolean,
			Gorge:.*,
			Occupied:boolean,
			Arthur:@Comp301Tags.AVATAR,
			KnightArea:mp.shapes.AScaleRectangleInterface,
			Guard:@Comp301Tags.AVATAR,
			Lancelot:@Comp301Tags.AVATAR,
			GuardArea:mp.shapes.AScaleRectangleInterface,
			Galahad:@Comp301Tags.AVATAR,
			Robin:@Comp301Tags.AVATAR,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedProperties" value="
			KnightTurn:boolean,
			Gorge:.*,
			Occupied:boolean,
			Arthur:@Comp301Tags.AVATAR,
			KnightArea:mp.shapes.AScaleRectangleInterface,
			Guard:@Comp301Tags.AVATAR,
			Lancelot:@Comp301Tags.AVATAR,
			GuardArea:mp.shapes.AScaleRectangleInterface,
			Galahad:@Comp301Tags.AVATAR,
			Robin:@Comp301Tags.AVATAR,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedProperties" value=""/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedProperties" value=""/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedSignatures" value="
			failed:->void,
			passed:->void,
			approach:@Comp301Tags.AVATAR->void,
			say:String->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedSignatures" value="
			failed:->void,
			passed:->void,
			approach:@Comp301Tags.AVATAR->void,
			say:String->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedCalls" value="
			@Comp301Tags.LOCATABLE!setX:*->.*,
			@Comp301Tags.AVATAR!getHead:->@Comp301Tags.BOUNDED_SHAPE,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			mp.bridge.StringShape!setText:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			mp.shapes.Locatable!setX:*->.*,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			@Comp301Tags.AVATAR!getStringShape:->mp.bridge.StringShape,
			mp.shapes.Locatable!setY:*->.*,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedCalls" value="
			@Comp301Tags.LOCATABLE!setX:*->.*,
			@Comp301Tags.AVATAR!getHead:->@Comp301Tags.BOUNDED_SHAPE,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			mp.bridge.StringShape!setText:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			mp.shapes.Locatable!setX:*->.*,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			@Comp301Tags.AVATAR!getStringShape:->mp.bridge.StringShape,
			mp.shapes.Locatable!setY:*->.*,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.FACTORY_CLASS"/>
		<property name="expectedSignatures" value="
			consoleSceneViewFactoryMethod:->@Comp301Tags.CONSOLE_SCENE_VIEW,
			legsFactoryMethod:->@Comp301Tags.ANGLE,
			bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.FACTORY_CLASS"/>
		<property name="expectedSignatures" value="
			consoleSceneViewFactoryMethod:->@Comp301Tags.CONSOLE_SCENE_VIEW,
			legsFactoryMethod:->@Comp301Tags.ANGLE,
			bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.FACTORY_CLASS"/>
		<property name="expectedCalls" value="
			@Comp301Tags.LOCATABLE!setX:*->.*,
			mp.shapes.Locatable!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.ANGLE!move:*->.*,
			java.lang.Math!sin:double->double,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			@Comp301Tags.LOCATABLE!move:*->.*,
			mp.shapes.Locatable!setY:*->.*,
			mp.shapes.RotateLine!setAngle:double->void,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			mp.shapes.Locatable!setX:*->.*,
			java.lang.Math!cos:double->double,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			mp.shapes.RotateLine!setRadius:double->void,
			mp.shapes.Locatable!getX:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getHeight:*->.*,
			@Comp301Tags.LOCATABLE!getWidth:->int,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.FACTORY_CLASS"/>
		<property name="expectedCalls" value="
			@Comp301Tags.LOCATABLE!setX:*->.*,
			mp.shapes.Locatable!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.ANGLE!move:*->.*,
			java.lang.Math!sin:double->double,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			@Comp301Tags.LOCATABLE!move:*->.*,
			mp.shapes.Locatable!setY:*->.*,
			mp.shapes.RotateLine!setAngle:double->void,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			mp.shapes.Locatable!setX:*->.*,
			java.lang.Math!cos:double->double,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			mp.shapes.RotateLine!setRadius:double->void,
			mp.shapes.Locatable!getX:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getHeight:*->.*,
			@Comp301Tags.LOCATABLE!getWidth:->int,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedInterfaces" value="
			mp.shapes.Locatable,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedInterfaces" value="
			mp.shapes.Locatable,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedInterfaces" value="
			mp.shapes.Locatable,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedInterfaces" value="
			mp.shapes.Locatable,
		"/>
	</module>
	<module name="ExpectedSuperTypes">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSuperTypes" value="
			@Comp301Tags.BOUNDED_SHAPE,
		"/>
	</module>
	<module name="ExpectedSuperTypes">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSuperTypes" value="
			@Comp301Tags.BOUNDED_SHAPE,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			Legs:@Comp301Tags.ANGLE,
			Head:@Comp301Tags.BOUNDED_SHAPE,
			StringShape:mp.bridge.StringShape,
			Arms:@Comp301Tags.ANGLE,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			Legs:@Comp301Tags.ANGLE,
			Head:@Comp301Tags.BOUNDED_SHAPE,
			StringShape:mp.bridge.StringShape,
			Arms:@Comp301Tags.ANGLE,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value=""/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value=""/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSignatures" value="
			move:int;int->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSignatures" value="
			move:int;int->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedCalls" value="
			@Comp301Tags.LOCATABLE!setX:*->.*,
			mp.shapes.Locatable!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.ANGLE!move:*->.*,
			java.lang.Math!sin:double->double,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			@Comp301Tags.LOCATABLE!move:*->.*,
			mp.shapes.Locatable!setY:*->.*,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			mp.shapes.Locatable!setX:*->.*,
			java.lang.Math!cos:double->double,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			mp.shapes.Locatable!getX:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getHeight:*->.*,
			@Comp301Tags.LOCATABLE!getWidth:->int,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedCalls" value="
			@Comp301Tags.LOCATABLE!setX:*->.*,
			mp.shapes.Locatable!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.ANGLE!move:*->.*,
			java.lang.Math!sin:double->double,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			@Comp301Tags.LOCATABLE!move:*->.*,
			mp.shapes.Locatable!setY:*->.*,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			mp.shapes.Locatable!setX:*->.*,
			java.lang.Math!cos:double->double,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			mp.shapes.Locatable!getX:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getHeight:*->.*,
			@Comp301Tags.LOCATABLE!getWidth:->int,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedInterfaces" value="
			mp.shapes.Locatable,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedInterfaces" value="
			mp.shapes.Locatable,
		"/>
	</module>
	<module name="ExpectedSuperTypes">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSuperTypes" value="
			@Comp301Tags.BOUNDED_SHAPE,
		"/>
	</module>
	<module name="ExpectedSuperTypes">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSuperTypes" value="
			@Comp301Tags.BOUNDED_SHAPE,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedInterfaces" value="
			mp.shapes.Locatable,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedInterfaces" value="
			mp.shapes.Locatable,
		"/>
	</module>
	<module name="ExpectedSuperTypes">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSuperTypes" value="
			@Comp301Tags.BOUNDED_SHAPE,
		"/>
	</module>
	<module name="ExpectedSuperTypes">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSuperTypes" value="
			@Comp301Tags.BOUNDED_SHAPE,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedInterfaces" value="
			mp.shapes.Locatable,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedInterfaces" value="
			mp.shapes.Locatable,
		"/>
	</module>
	<module name="ExpectedSuperTypes">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSuperTypes" value="
			@Comp301Tags.BOUNDED_SHAPE,
		"/>
	</module>
	<module name="ExpectedSuperTypes">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSuperTypes" value="
			@Comp301Tags.BOUNDED_SHAPE,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedInterfaces" value="
			mp.shapes.Locatable,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedInterfaces" value="
			mp.shapes.Locatable,
		"/>
	</module>
	<module name="ExpectedSuperTypes">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSuperTypes" value="
			@Comp301Tags.BOUNDED_SHAPE,
		"/>
	</module>
	<module name="ExpectedSuperTypes">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSuperTypes" value="
			@Comp301Tags.BOUNDED_SHAPE,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedProperties" value="
			RightLine:mp.shapes.RotateLine,
			LeftLine:mp.shapes.RotateLine,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedProperties" value="
			RightLine:mp.shapes.RotateLine,
			LeftLine:mp.shapes.RotateLine,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedProperties" value=""/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedProperties" value=""/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedSignatures" value="
			move:int;int->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedSignatures" value="
			move:int;int->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedCalls" value="
			@Comp301Tags.LOCATABLE!setX:*->.*,
			mp.shapes.Locatable!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			java.lang.Math!sin:double->double,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			@Comp301Tags.LOCATABLE!move:*->.*,
			mp.shapes.Locatable!setY:*->.*,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			mp.shapes.Locatable!setX:*->.*,
			java.lang.Math!cos:double->double,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			mp.shapes.Locatable!getX:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getHeight:*->.*,
			@Comp301Tags.LOCATABLE!getWidth:->int,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedCalls" value="
			@Comp301Tags.LOCATABLE!setX:*->.*,
			mp.shapes.Locatable!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			java.lang.Math!sin:double->double,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			@Comp301Tags.LOCATABLE!move:*->.*,
			mp.shapes.Locatable!setY:*->.*,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			mp.shapes.Locatable!setX:*->.*,
			java.lang.Math!cos:double->double,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			mp.shapes.Locatable!getX:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getHeight:*->.*,
			@Comp301Tags.LOCATABLE!getWidth:->int,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedInterfaces" value="
			mp.shapes.AScaleRectangleInterface,
			mp.shapes.BoundedShape,
			mp.shapes.Locatable,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedInterfaces" value="
			mp.shapes.AScaleRectangleInterface,
			mp.shapes.BoundedShape,
			mp.shapes.Locatable,
		"/>
	</module>
	<module name="ExpectedSuperTypes">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedSuperTypes" value="
			@<EMAIL>,
			@Comp301Tags.LOCATABLE,
		"/>
	</module>
	<module name="ExpectedSuperTypes">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedSuperTypes" value="
			@<EMAIL>,
			@Comp301Tags.LOCATABLE,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedSignatures" value="
			scale:int->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedSignatures" value="
			scale:int->void,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedInterfaces" value="
			mp.shapes.AScaleRectangleInterface,
			mp.shapes.BoundedShape,
			mp.shapes.Locatable,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedInterfaces" value="
			mp.shapes.AScaleRectangleInterface,
			mp.shapes.BoundedShape,
			mp.shapes.Locatable,
		"/>
	</module>
	<module name="ExpectedSuperTypes">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedSuperTypes" value="
			@<EMAIL>,
			@Comp301Tags.LOCATABLE,
		"/>
	</module>
	<module name="ExpectedSuperTypes">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedSuperTypes" value="
			@<EMAIL>,
			@Comp301Tags.LOCATABLE,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedSignatures" value="
			scale:int->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedSignatures" value="
			scale:int->void,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@<EMAIL>"/>
		<property name="expectedInterfaces" value="
			mp.shapes.BoundedShape,
			mp.shapes.Locatable,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@<EMAIL>"/>
		<property name="expectedInterfaces" value="
			mp.shapes.BoundedShape,
			mp.shapes.Locatable,
		"/>
	</module>
	<module name="ExpectedSuperTypes">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@<EMAIL>"/>
		<property name="expectedSuperTypes" value="
			@Comp301Tags.LOCATABLE,
		"/>
	</module>
	<module name="ExpectedSuperTypes">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@<EMAIL>"/>
		<property name="expectedSuperTypes" value="
			@Comp301Tags.LOCATABLE,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@<EMAIL>"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@<EMAIL>"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@<EMAIL>"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@<EMAIL>"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedInterfaces" value="
			mp.shapes.Locatable,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedInterfaces" value="
			mp.shapes.Locatable,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedInterfaces" value="
			mp.shapes.BoundedShape,
			mp.shapes.Locatable,
			mp.shapes.PolarPointInterface,
			mp.shapes.RotateLine,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedInterfaces" value="
			mp.shapes.BoundedShape,
			mp.shapes.Locatable,
			mp.shapes.PolarPointInterface,
			mp.shapes.RotateLine,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			Radius:double,
			Angle:double,
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			Radius:double,
			Angle:double,
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			Radius:double,
			Angle:double,
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			Radius:double,
			Angle:double,
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedSignatures" value="
			notify:String;Object;Object->void,
			move:int;int->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedSignatures" value="
			notify:String;Object;Object->void,
			move:int;int->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedCalls" value="
			mp.shapes.Locatable!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			java.lang.Math!sin:double->double,
			mp.shapes.PolarPointInterface!getAngle:->double,
			java.lang.Math!cos:double->double,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			mp.shapes.Locatable!getX:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			mp.shapes.PolarPointInterface!getRadius:->double,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedCalls" value="
			mp.shapes.Locatable!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			java.lang.Math!sin:double->double,
			mp.shapes.PolarPointInterface!getAngle:->double,
			java.lang.Math!cos:double->double,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			mp.shapes.Locatable!getX:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			mp.shapes.PolarPointInterface!getRadius:->double,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedInterfaces" value="
			mp.shapes.BoundedShape,
			mp.shapes.Locatable,
			mp.shapes.PolarPointInterface,
			mp.shapes.RotateLine,
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedInterfaces" value="
			mp.shapes.BoundedShape,
			mp.shapes.Locatable,
			mp.shapes.PolarPointInterface,
			mp.shapes.RotateLine,
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			Radius:double,
			Angle:double,
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			Radius:double,
			Angle:double,
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			Radius:double,
			Angle:double,
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			Radius:double,
			Angle:double,
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedSignatures" value="
			notify:String;Object;Object->void,
			move:int;int->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedSignatures" value="
			notify:String;Object;Object->void,
			move:int;int->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedCalls" value="
			@Comp301Tags.LOCATABLE!setX:*->.*,
			mp.shapes.Locatable!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			java.lang.Math!sin:double->double,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			java.util.List!add:Object->boolean,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			mp.shapes.PolarPointInterface!getAngle:->double,
			java.lang.Math!cos:double->double,
			mp.shapes.Locatable!getX:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			mp.shapes.PolarPointInterface!getRadius:->double,
			@Comp301Tags.LOCATABLE!getHeight:*->.*,
			@Comp301Tags.LOCATABLE!getWidth:->int,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedCalls" value="
			@Comp301Tags.LOCATABLE!setX:*->.*,
			mp.shapes.Locatable!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			java.lang.Math!sin:double->double,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			java.util.List!add:Object->boolean,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			mp.shapes.PolarPointInterface!getAngle:->double,
			java.lang.Math!cos:double->double,
			mp.shapes.Locatable!getX:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			mp.shapes.PolarPointInterface!getRadius:->double,
			@Comp301Tags.LOCATABLE!getHeight:*->.*,
			@Comp301Tags.LOCATABLE!getWidth:->int,
		"/>
	</module>
	<module name="ExpectedClassInstantiations">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="instantiations" value="
			java.util.ArrayList,
		"/>
	</module>
	<module name="ExpectedClassInstantiations">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="instantiations" value="
			java.util.ArrayList,
		"/>
	</module>
