#,Time,%Passes,Change,Test,Pass,Partial,Fail,Untested
0,Wed Jul 09 21:17:47 EDT 2025,0,0,A4Style, , ,A4CommonPropertiesAreInherited+ A4CommonSignaturesAreInherited+ A4Encapsulation+ A4InterfaceAsType+ A4MnemonicNames+ A4NamedConstants+ A4NamingConventions+ A4NoHiddenFields+ A4NoStarImports+ A4NonPublicAccessModifiersMatched+ A4PackageDeclarations+ A4PublicMethodsOverride+ A4SimplifyBooleanExpressions+ A4SimplifyBooleanReturns+ AssertingBridgeSceneDynamics+ ,AbstractBoundedShape AbstractLocatable AbstractionAsyncArthurAnimation AbstractionLockstepAvatars AbstractionSyncArthurAnimation AbstractionWaitingAvatars ArthurIsNotAContortionist AsyncArthurAnimation AsyncGalahadAnimation AsyncLancelotAnimation AsyncRobinAnimation BridgeSceneSingletonFromFactory BroadcastingClearanceManagerFactoryMethodDefined BroadcastingClearanceManagerSingletonFromFactory CheckedImpossibleAngle LockstepAvatars SceneControllerApproachButtonProperty SceneControllerButtonDynamics SceneControllerCallsApproach SceneControllerCallsFailed SceneControllerCallsPassed SceneControllerCallsSay SceneControllerCallsSetEnabled SceneControllerISAPropertyChangeListener SceneControllerISAnActionListener SceneControllerPassedButtonProperty SceneControllerRegistersAsActionListener SceneControllerRegistersAsPropertyChangeListener SceneControllerSayButtonProperty SceneControllerSingletonFromFactory SyncArthurAnimation SyncGalahadAnimation SyncLancelotAnimation SyncRobinAnimation TaggedBoundedShape TaggedImpossibleAngle TaggedLegs TaggedLocatable TaggedRestrictedLine WaitingAvatars ,
