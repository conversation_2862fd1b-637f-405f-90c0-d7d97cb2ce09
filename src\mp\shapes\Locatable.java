package mp.shapes;
import util.annotations.Tags;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
@Tags({Comp301Tags.LOCATABLE})
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public interface Locatable {
    int getX();
    void setX(final int x);
    int getY();
    void setY(final int y);
}

// Abstract base class that provides common implementation for Locatable
abstract class AbstractLocatable implements Locatable {
    protected int x, y;

    public AbstractLocatable() {
        this.x = 0;
        this.y = 0;
    }

    public AbstractLocatable(int x, int y) {
        this.x = x;
        this.y = y;
    }

    @Override
    public int getX() {
        return x;
    }

    @Override
    public void setX(int x) {
        this.x = x;
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(int y) {
        this.y = y;
    }
}
