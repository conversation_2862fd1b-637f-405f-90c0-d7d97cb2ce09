*PRE_OUTPUT*
>>Running suite BridgeSceneScroll
<<
>>Running test BridgeSceneScrollMethodDefined
<<
>>Running test BridgeSceneArthurScrollLeftArmTestCase
<<
>>Running test BridgeSceneGalahadScrollLeftArmTestCase
<<
>>Running test BridgeSceneLancelotScrollLeftArmTestCase
<<
>>Running test BridgeSceneRobinScrollLeftArmTestCase
<<
>>Running test BridgeSceneArthurScrollRightLegTestCase
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory method returns null object
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
LegsFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
AvatarCallsLegFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
TaggedConsoleSceneView,100.0% complete,0.0,0.0,
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
>>Test Result:
Locatable_IS_A_PropertyListenerRegisterer,100.0% complete,0.0,0.0,
<<
>>Test Result:
LocatablePropertyChangeListenersProperty,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BoundedShapeAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
BridgeSceneScrollMethodDefined,0.0% complete,0.0,2.0,main\BridgeSceneImpl.java:19: Missing signature @scroll:int;int->void//EC in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. [ExpectedSignatures]
<<
>>Test Result:
BridgeSceneArthurScrollLeftArmTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
>>Test Result:
BridgeSceneGalahadScrollLeftArmTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
>>Test Result:
BridgeSceneLancelotScrollLeftArmTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
>>Test Result:
BridgeSceneRobinScrollLeftArmTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
>>Test Result:
BridgeSceneArthurScrollRightLegTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
*END_OUTPUT*
