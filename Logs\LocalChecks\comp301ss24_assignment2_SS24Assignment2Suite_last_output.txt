*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,mp\bridge\AbstractImageShape.java:37:26: 'x' hides a field. [HiddenField]mp\bridge\AbstractImageShape.java:47:26: 'y' hides a field. [HiddenField]mp\bridge\AbstractStringShape.java:21:32: 'text' hides a field. [HiddenField]mp\bridge\AbstractStringShape.java:31:26: 'x' hides a field. [HiddenField]mp\bridge\AbstractStringShape.java:41:26: 'y' hides a field. [HiddenField]mp\bridge\StringShape.java:27:32: 'text' hides a field. [HiddenField]mp\bridge\StringShape.java:37:26: 'x' hides a field. [HiddenField]mp\bridge\StringShape.java:47:26: 'y' hides a field. [HiddenField]mp\shapes\BoundedShape.java:19:51: 'width' hides a field. [HiddenField]mp\shapes\BoundedShape.java:19:62: 'height' hides a field. [HiddenField]mp\shapes\BoundedShape.java:36:30: 'width' hides a field. [HiddenField]mp\shapes\BoundedShape.java:41:31: 'height' hides a field. [HiddenField]mp\shapes\Locatable.java:24:34: 'x' hides a field. [HiddenField]mp\shapes\Locatable.java:24:41: 'y' hides a field. [HiddenField]mp\shapes\Locatable.java:35:26: 'x' hides a field. [HiddenField]mp\shapes\Locatable.java:45:26: 'y' hides a field. [HiddenField]mp\shapes\PolarPointInterface.java:12:38: 'radius' hides a field. [HiddenField]mp\shapes\PolarPointInterface.java:12:53: 'angle' hides a field. [HiddenField]mp\shapes\RotateLine.java:34:26: 'x' hides a field. [HiddenField]mp\shapes\RotateLine.java:45:26: 'y' hides a field. [HiddenField]
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamedConstants,100.0% complete,10.0,10.0,
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,78.57142857142857% complete,3.9,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,100.0% complete,10.0,10.0,
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,0.0% complete,0.0,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonPropertiesAreInherited,17.75147928994083% complete,1.2,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,100.0% complete,7.0,7.0,
<<
*END_OUTPUT*
