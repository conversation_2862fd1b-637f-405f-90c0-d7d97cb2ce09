package mp.shapes;

import java.beans.PropertyChangeEvent;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.models.PropertyListenerRegisterer;
import mp.bridge.BridgeScene;
import mp.bridge.Avatar;
import tags301.Comp301Tags;

@Tags({Comp301Tags.CONSOLE_SCENE_VIEW})
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class ConsoleScene implements ConsoleSceneInterface{
	private static ConsoleSceneInterface instance;

	@Override
	public void propertyChange(PropertyChangeEvent evt) {
		System.out.println("Property changed: " + evt.getPropertyName() +
                          " from " + evt.getOldValue() +
                          " to " + evt.getNewValue() +
                          " on " + evt.getSource().getClass().getSimpleName());
	}

	public static ConsoleSceneInterface consoleSceneViewFactoryMethod() {
	    if (instance == null) {
	      instance = new ConsoleScene();
	    }
	    return instance;
	}

	public static void registerWithBridgeScene(BridgeScene bridgeScene, ConsoleSceneInterface consoleScene) {
        // Register as observer for all atomic shapes in the scene
        registerWithAvatar(bridgeScene.getArthur(), consoleScene);
        registerWithAvatar(bridgeScene.getLancelot(), consoleScene);
        registerWithAvatar(bridgeScene.getRobin(), consoleScene);
        registerWithAvatar(bridgeScene.getGalahad(), consoleScene);
        registerWithAvatar(bridgeScene.getGuard(), consoleScene);

        // Register with standing areas
        if (bridgeScene.getKnightArea() instanceof PropertyListenerRegisterer) {
            ((PropertyListenerRegisterer) bridgeScene.getKnightArea()).addPropertyChangeListener(consoleScene);
        }
        if (bridgeScene.getGuardArea() instanceof PropertyListenerRegisterer) {
            ((PropertyListenerRegisterer) bridgeScene.getGuardArea()).addPropertyChangeListener(consoleScene);
        }
    }

    private static void registerWithAvatar(Avatar avatar, ConsoleSceneInterface consoleScene) {
        // Register with head
        if (avatar.getHead() instanceof PropertyListenerRegisterer) {
            ((PropertyListenerRegisterer) avatar.getHead()).addPropertyChangeListener(consoleScene);
        }

        // Register with speech bubble
        if (avatar.getStringShape() instanceof PropertyListenerRegisterer) {
            ((PropertyListenerRegisterer) avatar.getStringShape()).addPropertyChangeListener(consoleScene);
        }

        // Register with arms and legs (composite shapes)
        registerWithCompositeShape(avatar.getArms(), consoleScene);
        registerWithCompositeShape(avatar.getLegs(), consoleScene);
    }

    private static void registerWithCompositeShape(Object shape, ConsoleSceneInterface consoleScene) {
        // For composite shapes like Shape (arms/legs), we need to register with their components
        if (shape instanceof mp.bridge.Shape) {
            mp.bridge.Shape compositeShape = (mp.bridge.Shape) shape;
            if (compositeShape.getLeftLine() instanceof PropertyListenerRegisterer) {
                ((PropertyListenerRegisterer) compositeShape.getLeftLine()).addPropertyChangeListener(consoleScene);
            }
            if (compositeShape.getRightLine() instanceof PropertyListenerRegisterer) {
                ((PropertyListenerRegisterer) compositeShape.getRightLine()).addPropertyChangeListener(consoleScene);
            }
        }
    }
}
