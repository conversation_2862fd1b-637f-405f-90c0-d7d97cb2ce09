package mp.shapes;

import java.beans.PropertyChangeEvent;

public class ConsoleScene implements ConsoleSceneInterface{
	 private static ConsoleSceneInterface instance;
	@Override
	public void propertyChange(PropertyChangeEvent evt) {
		System.out.println(evt);
	}
	public static ConsoleSceneInterface consoleSceneViewFactoryMethod() {
	    if (instance == null) {
	      instance = new ConsoleScene();
	    }
	    return instance;
	  }
}
