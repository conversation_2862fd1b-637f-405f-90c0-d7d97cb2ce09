package mp.shapes;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;

@Tags({Comp301Tags.LOCATABLE, Comp301Tags.BOUNDED_SHAPE})
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public abstract class AbstractBoundedShape extends AbstractLocatable implements BoundedShape {
    protected int width, height;
    
    public AbstractBoundedShape(int x, int y, int width, int height) {
        super(x, y);
        this.width = width;
        this.height = height;
    }
    
    @Override
    public int getWidth() {
        return width;
    }
    
    @Override
    public void setWidth(int width) {
        int oldWidth = this.width;
        this.width = width;
        notify("Width", oldWidth, width);
    }
    
    @Override
    public int getHeight() {
        return height;
    }
    
    @Override
    public void setHeight(int height) {
        int oldHeight = this.height;
        this.height = height;
        notify("Height", oldHeight, height);
    }
}
