package mp.shapes;

public class Gorge implements GetRectangle{
    int start = 950;
    int top = 0;
    int height = 1000;
    int num1 = 0;
    int num2 = -1000;
    int upper = 400;
    int lower = 100;
    RotateLine leftLine;
    RotateLine rightLine;
    AScaleRectangleInterface rectangle;
    public Gorge(final int x) {
        leftLine = new RotatingLine();
        leftLine.setRadius(height);
        leftLine.setAngle((Math.PI/2));
        leftLine.setX(x);
        leftLine.setY(top);
        leftLine.move(num1, num2);
        
        rightLine = new RotatingLine();
        rightLine.setRadius(height);
        rightLine.setAngle(Math.PI/2);
        rightLine.setX(start);
        rightLine.setY(top);
        rightLine.move(num1, num2);
        
        rectangle = new AScaleRectangle(x, upper, start - x, lower);
    }
    @Override
    public RotateLine getLeftLine() {
    	return leftLine;
    }
    @Override
    public RotateLine getRightLine(){
    	return rightLine;
    }
    @Override
    public AScaleRectangleInterface getRectangle() {
    	return rectangle;
    }
}