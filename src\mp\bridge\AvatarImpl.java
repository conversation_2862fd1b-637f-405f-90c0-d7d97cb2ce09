package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import main.StaticFactoryClass;
import mp.shapes.Locatable;
import mp.shapes.Moveable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class AvatarImpl implements Avatar{
    private final ImageShape head;
    private final StringShape speech;
    private final Angle arms;
    private final Angle legs;

    public AvatarImpl(final ImageShape h) {
        this.head = h;
        this.speech = new SpeechBubble();
        this.arms = StaticFactoryClass.legsFactoryMethod();
        this.legs = StaticFactoryClass.legsFactoryMethod();
        layoutAtOrigin();
    }
    private void layoutAtOrigin(){
    }
    @Override
    public ImageShape getHead(){ 
    	return head; 
    }
    @Override
    public StringShape getStringShape() {
        return speech;
    }
    @Override
    public Angle getArms(){
    	return arms; 
    }
    @Override
    public Angle getLegs(){ 
    	return legs; 
    }
    @Override
    public void move(final int deltaX, final int deltaY) {
        head.setX(head.getX() + deltaX);
        head.setY(head.getY() + deltaY);  
        arms.move(deltaX, deltaY);
        legs.move(deltaX, deltaY);
        speech.setX(speech.getX() + deltaX);
        speech.setY(speech.getY() + deltaY);
        layoutAtOrigin();
    }
}