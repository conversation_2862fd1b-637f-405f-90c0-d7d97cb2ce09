package mp.bridge;

import mp.shapes.Locatable;

public interface StringShape extends Locatable{
    String getText();
    void setText(String at);
}

// Abstract base class that provides common implementation for StringShape
abstract class AbstractStringShape implements StringShape {
    protected String text;
    protected int x, y;

    public AbstractStringShape(String defaultText) {
        this.text = defaultText;
        this.x = 0;
        this.y = 0;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public void setText(String text) {
        this.text = text;
    }

    @Override
    public int getX() {
        return x;
    }

    @Override
    public void setX(int x) {
        this.x = x;
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(int y) {
        this.y = y;
    }
}
