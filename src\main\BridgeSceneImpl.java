package main;
import mp.bridge.ArthurHead;
import mp.bridge.LancelotHead;
import mp.bridge.RobinHead;
import mp.bridge.GalahadHead;
import mp.bridge.GuardHead;
import mp.bridge.Avatar;
import mp.bridge.AvatarImpl;
import mp.bridge.BridgeScene;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScaleRectangle;
import mp.shapes.AScaleRectangleInterface;
import mp.shapes.GetRectangle;
@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int SOME_AX = 10;
    public static final int SOME_AY = 50;
    public static final int LANCELOT_CONSTANT = 8;
    public static final int ROBIN_CONSTANT = 15;
    public static final int GAL_CONSTANT = 22;
    public static final int GUARD_CONSTANT = 30;
    private GetRectangle gorge;
    private Avatar cur;
    private AScaleRectangleInterface knightArea;
    private AScaleRectangleInterface guardArea;
    private boolean knightTurn = false;
    private static final int AREA_AX = 500;
    private static final int KNIGHT_AY = 600; 
    private static final int GUARD_AY = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean occupied = false;
    private static final int GORGE_AX = 750;
    private static int number = 0;
    int constant = 50;
    public BridgeSceneImpl() {
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());
      arthur.move(SOME_AX, SOME_AY);
      lancelot.move(SOME_AX*LANCELOT_CONSTANT, SOME_AY);
      robin.move(SOME_AX*ROBIN_CONSTANT, SOME_AY);
      galahad.move(SOME_AX*GAL_CONSTANT,SOME_AY);
      guard.move(AREA_AX,GUARD_AY);
      gorge = new Gorge(GORGE_AX);
      knightArea = new AScaleRectangle(AREA_AX,KNIGHT_AY,AREA_WIDTH,AREA_HEIGHT);
      guardArea = new AScaleRectangle(AREA_AX,GUARD_AY,AREA_WIDTH,AREA_HEIGHT);
    }
    @Override
    public void passed(){
    	if(!knightTurn){
    		cur.move(AREA_AX, KNIGHT_AY);
    		occupied = false;
    	}
    }
    @Override
    public void failed(){
    	if(occupied) {
    		if(!knightTurn) {
    	cur.getHead().setX(GORGE_AX);
    	cur.getHead().setY(number);
    	number += constant;
    	occupied = !occupied;}
    		else {guard.getHead().setX(GORGE_AX);
        	guard.getHead().setY(number);
        	number += constant;}
    		}
    }
    @Override
    public void approach(final Avatar avatar){
    	if(!occupied) {avatar.move(AREA_AX, KNIGHT_AY);}
    	occupied = true;
    	cur = avatar;
    }
    @Override
    public void say(final String say){
    	if(occupied){
    		if(!knightTurn){guard.getStringShape().setText(say);knightTurn= !knightTurn;} 
    		else {cur.getStringShape().setText(say);knightTurn = !knightTurn;}
    	} else {return;}
    	return;
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
    @Override
    public AScaleRectangleInterface getKnightArea() {
        return knightArea;
    }
    @Override
    public AScaleRectangleInterface getGuardArea() {
        return guardArea;
    }
    @Override
    public GetRectangle getGorge() {return gorge;}
    @Override
    public boolean getOccupied() {return occupied;}
    @Override
    public boolean getKnightTurn() {return knightTurn;}
}