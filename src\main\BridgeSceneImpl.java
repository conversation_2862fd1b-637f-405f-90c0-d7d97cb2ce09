package main;
import mp.bridge.ArthurHead;
import mp.bridge.LancelotHead;
import mp.bridge.RobinHead;
import mp.bridge.GalahadHead;
import mp.bridge.GuardHead;
import mp.bridge.Avatar;
import mp.bridge.AvatarImpl;
import mp.bridge.BridgeScene;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScaleRectangle;
import mp.shapes.AScaleRectangleInterface;
import mp.shapes.GetRectangle;
import util.annotations.Visible;
@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int SOME_AX = 10;
    public static final int SOME_AY = 50;
    public static final int LANCELOT_CONSTANT = 8;
    public static final int ROBIN_CONSTANT = 15;
    public static final int GAL_CONSTANT = 22;
    public static final int GUARD_CONSTANT = 30;
    private GetRectangle gorge;
    private Avatar cur;
    private AScaleRectangleInterface knightArea;
    private AScaleRectangleInterface guardArea;
    private boolean knightTurn = false;
    private static final int AREA_AX = 500;
    private static final int KNIGHT_AY = 600; 
    private static final int GUARD_AY = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean occupied = false;
    private static final int GORGE_AX = 750;
    private static final int RIGHT_SIDE_X = 900; // Right side of gorge for passed knights
    private static int number = 0;
    private static final int CONSTANT = 50;
    public BridgeSceneImpl() {
      // Create avatars
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());

      // Place knights on left side of gorge, with no knight in standing area
      arthur.move(SOME_AX, SOME_AY);
      lancelot.move(SOME_AX*LANCELOT_CONSTANT, SOME_AY);
      robin.move(SOME_AX*ROBIN_CONSTANT, SOME_AY);
      galahad.move(SOME_AX*GAL_CONSTANT, SOME_AY);

      // Place guard in guard standing area
      guard.move(AREA_AX, GUARD_AY);

      // Create gorge and standing areas
      gorge = new Gorge(GORGE_AX);
      knightArea = new AScaleRectangle(AREA_AX, KNIGHT_AY, AREA_WIDTH, AREA_HEIGHT);
      guardArea = new AScaleRectangle(AREA_AX, GUARD_AY, AREA_WIDTH, AREA_HEIGHT);
    }
    @Override
    public void passed(){
    	if(occupied && !knightTurn) { // Only if occupied and it's guard's turn
    		cur.move(RIGHT_SIDE_X, KNIGHT_AY); // Move knight to right side of gorge
    		occupied = false;
    		cur = null;
    		knightTurn = false; // Reset for next knight
    	}
    }
    @Override
    public void failed(){
    	if(occupied) {
    		if(!knightTurn) {
    			// Guard's turn to speak, so knight fails
    			cur.getHead().setX(GORGE_AX);
    			cur.getHead().setY(number);
    			number += CONSTANT;
    			occupied = false; // Knight area becomes unoccupied
    			cur = null;
    			knightTurn = false; // Reset for next knight
    		} else {
    			// Knight's turn to speak, so guard fails
    			guard.getHead().setX(GORGE_AX);
    			guard.getHead().setY(number);
    			number += CONSTANT;
    			// Knight area remains occupied, guard just falls
    		}
    	}
    }
    @Override
    public void approach(final Avatar avatar){
    	if(!occupied) {
    		avatar.move(AREA_AX, KNIGHT_AY);
    		occupied = true;
    		cur = avatar;
    	}
    	// Do nothing if already occupied
    }
    @Override
    public void say(final String say){
    	if(!occupied) {
    		return; // Do nothing if knight area is not occupied
    	}

    	if(!knightTurn) {
    		// Guard's turn to speak
    		guard.getStringShape().setText(say);
    		knightTurn = true; // Next turn will be knight's
    	} else {
    		// Knight's turn to speak
    		cur.getStringShape().setText(say);
    		knightTurn = false; // Next turn will be guard's
    	}
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
    @Override
    public AScaleRectangleInterface getKnightArea() {
        return knightArea;
    }
    @Override
    public AScaleRectangleInterface getGuardArea() {
        return guardArea;
    }
    @Override
    public GetRectangle getGorge() {return gorge;}
    @Override
    public boolean getOccupied() {return occupied;}
    @Override
    public boolean getKnightTurn() {return knightTurn;}

    @Override
    @Visible(false)
    public Avatar getInteractingKnight() {
        return occupied ? cur : null;
    }
}