package mp.shapes;
import util.annotations.Tags;
import tags301.Comp301Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
@Tags({Comp301Tags.BOUNDED_SHAPE, Comp301Tags.LOCATABLE})
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public interface BoundedShape extends Locatable{
    int getWidth();
    int getHeight();
    void setWidth(int x);
    void setHeight(int x);
}

// Abstract base class for BoundedShape implementations
abstract class AbstractBoundedShape extends AbstractLocatable implements BoundedShape {
    protected int width, height;

    public AbstractBoundedShape(int x, int y, int width, int height) {
        super(x, y);
        this.width = width;
        this.height = height;
    }

    @Override
    public int getWidth() {
        return width;
    }

    @Override
    public int getHeight() {
        return height;
    }

    @Override
    public void setWidth(int width) {
        this.width = width;
    }

    @Override
    public void setHeight(int height) {
        this.height = height;
    }
}